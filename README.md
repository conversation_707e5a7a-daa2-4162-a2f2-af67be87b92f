# Virtual Power Plant Resource Manager

## 项目简介

虚拟电厂资源管理器是一个基于Java 8和Spring Boot的微服务应用，用于管理和协调分布式能源资源(DERs)。该系统提供集中化的资源管理、监控和控制功能，将多个能源资源聚合成统一的发电实体。

### 核心功能

- **资源管理**: 集中管理分布式能源资源，包括太阳能板、风力发电机、储能系统等
- **实时监控**: 持续监控资源状态、性能和可用性
- **电网集成**: 与电力系统运营和市场参与无缝集成
- **可扩展性**: 支持管理数千个分布在多个位置的分布式资源
- **高可靠性**: 为关键电力系统运营提供高可用性和容错能力

## 技术栈

### 核心技术
- **Java 8**: 主要编程语言
- **Spring Boot 2.x**: 应用框架
- **Maven**: 构建和依赖管理
- **MyBatis Plus**: 数据访问层
- **Redis**: 缓存服务
- **MySQL/PostgreSQL**: 主数据库

### 框架和库
- **Spring Framework**: 核心应用框架
- **Spring Web**: REST API支持
- **Spring Data**: 数据访问抽象
- **Spring Security**: 认证和授权
- **Spring Boot Actuator**: 监控和管理端点
- **Eureka**: 服务注册与发现

## 项目结构

```
virtual-power-plant-resource-manager/
├── matrix-vpp-resource-manager-common/    # 共享工具和公共组件
├── matrix-vpp-resource-manager-core/      # 主要应用逻辑
├── matrix-vpp-resource-manager-web/       # Web应用入口
├── memory-bank/                           # 项目文档和上下文
├── pom.xml                               # 根POM文件
└── README.md                             # 项目说明文档
```

### 模块说明

- **matrix-vpp-resource-manager-common**: 包含共享的工具类、常量定义和公共组件
- **matrix-vpp-resource-manager-core**: 包含核心业务逻辑、服务层、数据访问层
- **matrix-vpp-resource-manager-web**: Web层，包含控制器、配置文件和应用启动类

## 环境要求

### 必需软件
1. **Java 8 JDK**: Oracle JDK 或 OpenJDK 8
2. **Maven 3.x**: 最新稳定版本
3. **Git**: 版本控制系统
4. **IDE**: IntelliJ IDEA (推荐) 或 Eclipse
5. **数据库**: MySQL 5.7+ 或 PostgreSQL 9.6+
6. **Redis**: 用于缓存服务

### 推荐配置
- **内存**: 最少4GB RAM
- **存储**: 至少10GB可用空间
- **网络**: 稳定的网络连接用于依赖下载

## 安装和运行

### 1. 克隆项目

```bash
git clone <repository-url>
cd virtual-power-plant-resource-manager
```

### 2. 环境配置

#### 数据库配置
1. 创建数据库实例
2. 配置数据库连接信息在 `application.yml` 中

#### Redis配置
1. 启动Redis服务
2. 配置Redis连接信息在 `application.yml` 中

### 3. 构建项目

```bash
# 清理并编译
mvn clean compile

# 运行测试
mvn test

# 打包应用
mvn package

# 安装到本地仓库
mvn install
```

### 4. 运行应用

#### 开发环境运行

```bash
# 方式1: 使用Maven插件运行
mvn spring-boot:run -pl matrix-vpp-resource-manager-web

# 方式2: 运行打包后的JAR文件
java -jar matrix-vpp-resource-manager-web/target/matrix-vpp-resource-manager-web-*******.jar
```

#### 生产环境运行

```bash
# 使用生产配置运行
java -jar -Dspring.profiles.active=prod matrix-vpp-resource-manager-web-*******.jar
```

### 5. 验证运行状态

应用启动后，可以通过以下方式验证：

- **健康检查**: http://localhost:8002/actuator/health
- **应用信息**: http://localhost:8002/actuator/info
- **API文档**: http://localhost:8002/swagger-ui.html (如果配置了Swagger)

## 配置说明

### 主要配置文件

- `application.yml`: 主配置文件
- `application-dev.yml`: 开发环境配置
- `application-prod.yml`: 生产环境配置

### 关键配置项

```yaml
server:
  port: 8002  # 应用端口

spring:
  redis:
    host: ************
    password: sA123456
    port: 26379
  application:
    name: roommonitor

eureka:
  client:
    service-url:
      defaultZone: ***********************************/eureka/
```

## API文档

### 主要API端点

#### 虚拟电厂管理
- `POST /api/v1/vpp` - 创建虚拟电厂
- `GET /api/v1/vpp/{id}` - 获取虚拟电厂详情
- `PUT /api/v1/vpp/{id}` - 更新虚拟电厂
- `DELETE /api/v1/vpp/{id}` - 删除虚拟电厂
- `GET /api/v1/vpp/page` - 分页查询虚拟电厂列表

#### 站点管理
- `POST /api/v1/sites` - 创建站点
- `GET /api/v1/sites/{id}` - 获取站点详情
- `PUT /api/v1/sites/{id}` - 更新站点
- `DELETE /api/v1/sites/{id}` - 删除站点
- `GET /api/v1/sites/page` - 分页查询站点列表

#### 用户管理
- `POST /api/v1/users` - 创建用户
- `GET /api/v1/users/{id}` - 获取用户详情
- `PUT /api/v1/users/{id}` - 更新用户
- `DELETE /api/v1/users/{id}` - 删除用户

### API响应格式

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 响应数据
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## 开发指南

### 代码结构

```
src/main/java/com/cet/electric/vpp/resourcemanager/
├── core/
│   ├── controller/     # 控制器层
│   ├── service/        # 服务层
│   ├── mapper/         # 数据访问层
│   ├── config/         # 配置类
│   └── util/           # 工具类
└── web/
    └── ResourceManagerApplication.java  # 应用启动类
```

### 开发规范

1. **代码风格**: 遵循Java编码规范
2. **命名规范**: 使用驼峰命名法
3. **注释规范**: 重要方法和类必须添加注释
4. **异常处理**: 统一异常处理机制
5. **日志记录**: 使用SLF4J进行日志记录

### 数据库操作模式

```java
// 使用BaseMapperImpl进行CRUD操作
@Autowired
private VppMapper vppMapper;

// 查询操作
VppBO vpp = vppMapper.selectById(id);
List<VppBO> vpps = vppMapper.selectList(queryWrapper);

// 插入操作
vppMapper.insert(vpp);

// 更新操作
vppMapper.insert(vpp); // 如果ID存在则更新

// 删除操作
vppMapper.deleteById(id);
```

## 测试

### 运行测试

```bash
# 运行所有测试
mvn test

# 运行特定模块测试
mvn test -pl matrix-vpp-resource-manager-core

# 生成测试报告
mvn surefire-report:report
```

### 测试覆盖率

```bash
# 生成覆盖率报告
mvn jacoco:report
```

## 部署

### Docker部署

```dockerfile
FROM openjdk:8-jre-alpine
COPY matrix-vpp-resource-manager-web/target/*.jar app.jar
EXPOSE 8002
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

### 构建和运行Docker镜像

```bash
# 构建镜像
docker build -t vpp-resource-manager:latest .

# 运行容器
docker run -d -p 8002:8002 --name vpp-resource-manager vpp-resource-manager:latest
```

## 监控和日志

### 应用监控

- **健康检查**: `/actuator/health`
- **指标监控**: `/actuator/metrics`
- **环境信息**: `/actuator/env`

### 日志配置

日志文件位置: `logs/application.log`

日志级别配置:
```yaml
logging:
  level:
    com.cet.electric.vpp: DEBUG
    org.springframework: INFO
```

## 故障排除

### 常见问题

1. **端口冲突**: 修改 `application.yml` 中的端口配置
2. **数据库连接失败**: 检查数据库配置和网络连接
3. **Redis连接失败**: 检查Redis服务状态和配置
4. **内存不足**: 增加JVM堆内存设置

### 日志查看

```bash
# 查看应用日志
tail -f logs/application.log

# 查看错误日志
grep ERROR logs/application.log
```

## 贡献指南

1. Fork项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

## 许可证

本项目采用 [MIT License](LICENSE) 许可证。

## 联系方式

- **项目维护者**: mazhengqiang
- **邮箱**: [联系邮箱]
- **项目地址**: [项目仓库地址]

## 更新日志

### v******* (当前版本)
- 初始版本发布
- 实现基础的虚拟电厂资源管理功能
- 支持站点管理和用户管理
- 集成Spring Boot和MyBatis Plus
- 添加Redis缓存支持
- 实现RESTful API接口

---

**注意**: 在生产环境部署前，请确保所有配置项都已正确设置，特别是数据库连接、Redis配置和安全设置。
